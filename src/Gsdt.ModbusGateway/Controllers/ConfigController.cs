using Gsdt.ModbusGateway.Models;
using Gsdt.ModbusGateway.Models.Api;
using Gsdt.ModbusGateway.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;

namespace Gsdt.ModbusGateway.Controllers;

/// <summary>
/// 配置管理控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class ConfigController : ControllerBase
{
    private readonly ILogger<ConfigController> _logger;
    private readonly IGatewayConfigService _gatewayConfigService;
    private readonly IOptions<AppConfig> _appConfig;

    public ConfigController(
        ILogger<ConfigController> logger,
        IGatewayConfigService gatewayConfigService,
        IOptions<AppConfig> appConfig)
    {
        _logger = logger;
        _gatewayConfigService = gatewayConfigService;
        _appConfig = appConfig;
    }

    /// <summary>
    /// 获取当前网关配置
    /// </summary>
    [HttpGet("gateway")]
    public ActionResult<GatewayConfig> GetGatewayConfig()
    {
        try
        {
            var config = _gatewayConfigService.GetGatewayConfig();
            return Ok(config);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取网关配置时发生错误");
            return StatusCode(500, new { error = "获取网关配置失败", details = ex.Message });
        }
    }

    /// <summary>
    /// 获取应用程序配置
    /// </summary>
    [HttpGet("app")]
    public ActionResult<AppConfig> GetAppConfig()
    {
        return Ok(_appConfig.Value);
    }

    /// <summary>
    /// 重新加载网关配置
    /// </summary>
    [HttpPost("reload")]
    public async Task<ActionResult> ReloadConfig()
    {
        try
        {
            var success = await _gatewayConfigService.ReloadConfigAsync();
            if (success)
            {
                _logger.LogInformation("配置重新加载成功");
                return Ok(new { message = "配置重新加载成功" });
            }
            else
            {
                _logger.LogWarning("配置重新加载失败");
                return BadRequest(new { error = "配置重新加载失败" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重新加载配置时发生错误");
            return StatusCode(500, new { error = "重新加载配置失败", details = ex.Message });
        }
    }

    /// <summary>
    /// 列出可用的配置文件
    /// </summary>
    [HttpGet("files")]
    public ActionResult<List<string>> ListConfigFiles()
    {
        try
        {
            var configDir = Path.Combine(AppContext.BaseDirectory, "GatewayConfig");
            if (!Directory.Exists(configDir))
            {
                return Ok(new List<string>());
            }

            var files = Directory.GetFiles(configDir, "*.json")
                .Select(Path.GetFileName)
                .Where(name => !string.IsNullOrEmpty(name))
                .Cast<string>()
                .ToList();

            return Ok(files);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "列出配置文件时发生错误");
            return StatusCode(500, new { error = "列出配置文件失败", details = ex.Message });
        }
    }

    /// <summary>
    /// 获取配置文件内容
    /// </summary>
    [HttpGet("files/{fileName}")]
    public async Task<ActionResult<object>> GetConfigFile(string fileName)
    {
        try
        {
            if (string.IsNullOrEmpty(fileName) || !fileName.EndsWith(".json"))
            {
                return BadRequest(new { error = "无效的文件名" });
            }

            var configPath = Path.Combine(AppContext.BaseDirectory, "GatewayConfig", fileName);
            if (!System.IO.File.Exists(configPath))
            {
                return NotFound(new { error = "配置文件不存在" });
            }

            var content = await System.IO.File.ReadAllTextAsync(configPath);
            return Ok(new { fileName, content });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "读取配置文件 {FileName} 时发生错误", fileName);
            return StatusCode(500, new { error = "读取配置文件失败", details = ex.Message });
        }
    }

    /// <summary>
    /// 获取配置状态信息
    /// </summary>
    [HttpGet("status")]
    public ActionResult<object> GetConfigStatus()
    {
        try
        {
            var appConfig = _appConfig.Value;
            var currentConfig = _gatewayConfigService.GetGatewayConfig();
            
            return Ok(new
            {
                currentConfigFile = appConfig.GatewayConfigFile ?? "appsettings.json (fallback)",
                enableConfigReload = appConfig.EnableConfigReload,
                configReloadInterval = appConfig.ConfigReloadInterval,
                deviceCount = new
                {
                    tcpDevices = currentConfig.TcpTargetDevices.Count,
                    rtuDevices = currentConfig.RtuTargetDevices.Count,
                    totalDevices = currentConfig.TargetDevices.Count
                },
                routeCount = currentConfig.Routes.Count,
                listenPorts = currentConfig.ListenPorts
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取配置状态时发生错误");
            return StatusCode(500, new { error = "获取配置状态失败", details = ex.Message });
        }
    }
}
