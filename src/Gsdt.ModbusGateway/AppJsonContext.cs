using System.IO.Ports;
using System.Text.Json.Serialization;
using Gsdt.ModbusGateway.Controllers;
using Gsdt.ModbusGateway.Models;
using Gsdt.ModbusGateway.Models.Api;
using Gsdt.ModbusGateway.Services;

namespace Gsdt.ModbusGateway;

[JsonSourceGenerationOptions(
    PropertyNamingPolicy = JsonKnownNamingPolicy.CamelCase,
    WriteIndented = true)]
[JsonSerializable(typeof(AppConfig))]
[JsonSerializable(typeof(GatewayConfig))]
[JsonSerializable(typeof(TcpTargetDevice))]
[JsonSerializable(typeof(List<TcpTargetDevice>))]
[JsonSerializable(typeof(RtuTargetDevice))]
[JsonSerializable(typeof(List<RtuTargetDevice>))]
[JsonSerializable(typeof(RouteConfig))]
[JsonSerializable(typeof(List<RouteConfig>))]
[JsonSerializable(typeof(TargetDevice))]
[JsonSerializable(typeof(List<TargetDevice>))]
[JsonSerializable(typeof(TransformRout))]
[JsonSerializable(typeof(List<TransformRout>))]
[JsonSerializable(typeof(PortType))]
[JsonSerializable(typeof(System.IO.Ports.Parity))]
[JsonSerializable(typeof(System.IO.Ports.StopBits))]
[JsonSerializable(typeof(int[]))]
[JsonSerializable(typeof(List<int>))]
[JsonSerializable(typeof(SystemOverview))]
[JsonSerializable(typeof(ReconnectResult))]
[JsonSerializable(typeof(ResetResult))]
[JsonSerializable(typeof(VersionInfo))]
[JsonSerializable(typeof(DevicePerformanceSnapshot))]
[JsonSerializable(typeof(DeviceHealthStatus))]
[JsonSerializable(typeof(Dictionary<int, DevicePerformanceSnapshot>))]
[JsonSerializable(typeof(Dictionary<int, DeviceHealthStatus>))]
[JsonSerializable(typeof(string[]))]
[JsonSerializable(typeof(List<string>))]
[JsonSerializable(typeof(List<int>))]
[JsonSerializable(typeof(ConfigStatusResponse))]
[JsonSerializable(typeof(DeviceCountInfo))]
[JsonSerializable(typeof(ConfigFileResponse))]
[JsonSerializable(typeof(MessageResponse))]
[JsonSerializable(typeof(ErrorResponse))]
public partial class AppJsonContext : JsonSerializerContext;