using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using System.IO.Ports;
using System.Text.Json.Serialization;

namespace Gsdt.ModbusGateway.Models;

[DynamicallyAccessedMembers(DynamicallyAccessedMemberTypes.PublicProperties)]
public class RtuTargetDevice : TargetDevice
{
    public RtuTargetDevice()
    {
        PortType = PortType.Rtu;
    }
    
    /// <summary>
    /// 串口名称
    /// </summary>
    [Required(ErrorMessage = "串口名称是必需的")]
    [JsonPropertyName("PortName")]
    public string PortName { get; set; } = string.Empty;
    
    /// <summary>
    /// 波特率
    /// </summary>
    [Required(ErrorMessage = "波特率是必需的")]
    [Range(1, 115200, ErrorMessage = "波特率必须在1-115200之间")]
    public int BaudRate { get; set; } = 115200;
    
    /// <summary>
    /// 数据位
    /// </summary>
    [Range(5, 8, ErrorMessage = "数据位必须在5-8之间")]
    public int DataBits { get; set; } = 8;
    
    /// <summary>
    /// 停止位
    /// </summary>
    [Range(1, 2, ErrorMessage = "停止位必须在1-2之间")]
    public StopBits StopBits { get; set; } = StopBits.One;
    
    /// <summary>
    /// 奇偶校验
    /// </summary>
    public Parity Parity { get; set; } = Parity.None;
}